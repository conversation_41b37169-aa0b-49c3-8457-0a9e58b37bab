#include <iostream>
#include <vector>
#include <algorithm>
#include <climits>
using namespace std;

typedef long long llint;

llint calculateCost(int n, llint arr[], llint waterLevel) {
    llint cost = 0;
    int mid = n / 2;

    // Calculate cost for the symmetric pattern
    for (int i = 0; i < n; i++) {
        llint targetHeight;
        if (i <= mid) {
            // Descending part: waterLevel + (mid - i)
            targetHeight = waterLevel + (mid - i);
        } else {
            // Ascending part: waterLevel + (i - mid)
            targetHeight = waterLevel + (i - mid);
        }
        cost += abs(arr[i] - targetHeight);
    }

    return cost;
}

llint findMoves(int n, llint a[], llint b[]) {
    // Collect all possible water level candidates
    vector<llint> candidates;

    // Add all original heights as candidates
    for (int i = 0; i < n; i++) {
        candidates.push_back(a[i]);
        candidates.push_back(b[i]);
    }

    // Add heights adjusted for middle position
    int mid = n / 2;
    for (int i = 0; i < n; i++) {
        if (i <= mid) {
            candidates.push_back(a[i] - (mid - i));
            candidates.push_back(b[i] - (mid - i));
        } else {
            candidates.push_back(a[i] - (i - mid));
            candidates.push_back(b[i] - (i - mid));
        }
    }

    // Remove duplicates and sort
    sort(candidates.begin(), candidates.end());
    candidates.erase(unique(candidates.begin(), candidates.end()), candidates.end());

    llint minMoves = LLONG_MAX;

    // Try each candidate as water level
    for (llint waterLevel : candidates) {
        if (waterLevel < 0) continue; // Water level can't be negative

        llint costA = calculateCost(n, a, waterLevel);
        llint costB = calculateCost(n, b, waterLevel);
        llint totalCost = costA + costB;

        minMoves = min(minMoves, totalCost);
    }

    return minMoves;
}

int main() {
    int n;
    cin >> n;
    llint a[n], b[n];
    for (int i = 0; i < n; i++) cin >> a[i];
    for (int i = 0; i < n; i++) cin >> b[i];
    cout << findMoves(n, a, b) << endl;
    return 0;
}
