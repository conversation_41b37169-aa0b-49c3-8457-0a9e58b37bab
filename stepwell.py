def calculate_cost(n, arr, water_level):
    cost = 0
    mid = n // 2
    
    for i in range(n):
        if i <= mid:
            # Descending part: water_level + (mid - i)
            target_height = water_level + (mid - i)
        else:
            # Ascending part: water_level + (i - mid)
            target_height = water_level + (i - mid)
        cost += abs(arr[i] - target_height)
    
    return cost

def find_moves(n, a, b):
    # Collect all possible water level candidates
    candidates = set()
    
    # Add all original heights as candidates
    for i in range(n):
        candidates.add(a[i])
        candidates.add(b[i])
    
    # Add heights adjusted for middle position
    mid = n // 2
    for i in range(n):
        if i <= mid:
            candidates.add(a[i] - (mid - i))
            candidates.add(b[i] - (mid - i))
        else:
            candidates.add(a[i] - (i - mid))
            candidates.add(b[i] - (i - mid))
    
    # Filter out negative candidates
    candidates = [c for c in candidates if c >= 0]
    
    min_moves = float('inf')
    
    # Try each candidate as water level
    for water_level in candidates:
        cost_a = calculate_cost(n, a, water_level)
        cost_b = calculate_cost(n, b, water_level)
        total_cost = cost_a + cost_b
        
        min_moves = min(min_moves, total_cost)
    
    return min_moves

# Test with sample inputs
def test():
    # Sample 1
    n1 = 3
    a1 = [1, 2, 3]
    b1 = [3, 2, 2]
    result1 = find_moves(n1, a1, b1)
    print(f"Sample 1 result: {result1} (expected: 3)")
    
    # Sample 2
    n2 = 5
    a2 = [2, 3, 0, 1, 4]
    b2 = [3, 3, 2, 3, 1]
    result2 = find_moves(n2, a2, b2)
    print(f"Sample 2 result: {result2} (expected: 10)")

if __name__ == "__main__":
    test()
