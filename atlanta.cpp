#include <bits/stdc++.h>
using namespace std;

int getCheapestResult(int n, int k, int arr[][3]) {
    // Build adjacency list
    vector<vector<pair<int, int>>> adj(n);
    for (int i = 0; i < k; i++) {
        int u = arr[i][0], v = arr[i][1], w = arr[i][2];
        adj[u].push_back({v, w});
        adj[v].push_back({u, w});
    }

    // Find bridges using <PERSON><PERSON><PERSON>'s algorithm
    vector<bool> visited(n, false);
    vector<int> disc(n), low(n), parent(n, -1);
    set<pair<int, int>> bridges;
    int timer = 0;

    function<void(int)> bridgeUtil = [&](int u) {
        visited[u] = true;
        disc[u] = low[u] = timer++;

        for (auto& edge : adj[u]) {
            int v = edge.first;
            if (!visited[v]) {
                parent[v] = u;
                bridgeUtil(v);
                low[u] = min(low[u], low[v]);

                if (low[v] > disc[u]) {
                    bridges.insert({min(u, v), max(u, v)});
                }
            } else if (v != parent[u]) {
                low[u] = min(low[u], disc[v]);
            }
        }
    };

    for (int i = 0; i < n; i++) {
        if (!visited[i]) {
            bridgeUtil(i);
        }
    }
    

    // Find connected components without bridge edges
    vector<bool> vis(n, false);
    vector<vector<int>> components;

    function<void(int, vector<int>&)> dfs = [&](int u, vector<int>& component) {
        vis[u] = true;
        component.push_back(u);

        for (auto& edge : adj[u]) {
            int v = edge.first;
            pair<int, int> edgePair = {min(u, v), max(u, v)};

            if (!vis[v] && bridges.find(edgePair) == bridges.end()) {
                dfs(v, component);
            }
        }
    };

    for (int i = 0; i < n; i++) {
        if (!vis[i]) {
            vector<int> component;
            dfs(i, component);
            if (component.size() > 1) {
                components.push_back(component);
            }
        }
    }


    if (components.empty()) {
        return 0;
    }

    // Find the largest component
    vector<int> largestComponent;
    for (auto& comp : components) {
        if (comp.size() > largestComponent.size()) {
            largestComponent = comp;
        }
    }

    if (largestComponent.size() <= 1) {
        return 0;
    }

    // Create edges for MST of the largest component
    set<int> componentSet(largestComponent.begin(), largestComponent.end());
    vector<tuple<int, int, int>> edges;

    for (int i = 0; i < k; i++) {
        int u = arr[i][0], v = arr[i][1], w = arr[i][2];
        if (componentSet.count(u) && componentSet.count(v)) {
            edges.push_back({w, u, v});
        }
    }

    // Sort edges by weight for Kruskal's algorithm
    sort(edges.begin(), edges.end());

    // Union-Find for MST
    vector<int> parent(n);
    iota(parent.begin(), parent.end(), 0);

    function<int(int)> find = [&](int x) {
        return parent[x] == x ? x : parent[x] = find(parent[x]);
    };

    int mstCost = 0;
    int edgesUsed = 0;

    for (auto& edge : edges) {
        int w = get<0>(edge), u = get<1>(edge), v = get<2>(edge);
        int pu = find(u), pv = find(v);

        if (pu != pv) {
            parent[pu] = pv;
            mstCost += w;
            edgesUsed++;

            if (edgesUsed == largestComponent.size() - 1) {
                break;
            }
        }
    }

    return mstCost;
}

int main() {
    int n, k;
    cin >> n >> k;
    int arr[k][3];
    for (int i = 0; i < k; ++i) {
        cin >> arr[i][0];
        cin >> arr[i][1];
        cin >> arr[i][2];
    }
    int result = getCheapestResult(n, k, arr);
    cout << result;
    return 0;
}
