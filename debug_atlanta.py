from collections import defaultdict

def debug_test1():
    print("=== Test Case 1 Debug ===")
    edges = [
        (0, 6, 15),
        (0, 1, 10),
        (1, 2, 20),
        (1, 3, 30),
        (2, 3, 40),
        (2, 5, 50),
        (2, 4, 60)
    ]
    
    # Build graph
    graph = defaultdict(list)
    for u, v, w in edges:
        graph[u].append((v, w))
        graph[v].append((u, w))
    
    print("Graph adjacency:")
    for node in sorted(graph.keys()):
        print(f"  {node}: {graph[node]}")
    
    # Find bridges manually
    print("\nAnalyzing bridges:")
    print("  Edge (0,6): Removing disconnects 6 from rest -> BRIDGE")
    print("  Edge (0,1): Removing disconnects {0,6} from {1,2,3,4,5} -> BRIDGE") 
    print("  Edge (2,4): Removing disconnects 4 from rest -> BRIDGE")
    print("  Edge (2,5): Removing disconnects 5 from rest -> BRIDGE")
    
    print("\nAfter removing bridges, remaining components:")
    print("  Component 1: {1, 2, 3} with edges (1,2,20), (1,3,30), (2,3,40)")
    
    print("\nMST of component {1,2,3}:")
    print("  Edges sorted by weight: (1,2,20), (1,3,30), (2,3,40)")
    print("  MST: (1,2,20) + (1,3,30) = 50")

def debug_test2():
    print("\n=== Test Case 2 Debug ===")
    edges = [
        (0, 4, 70),
        (0, 1, 60),
        (0, 3, 10),
        (0, 2, 20),
        (1, 5, 50),
        (1, 2, 30),
        (1, 3, 80),
        (2, 3, 40)
    ]
    
    print("Analyzing bridges:")
    print("  Edge (0,4): Removing disconnects 4 from rest -> BRIDGE")
    print("  Edge (1,5): Removing disconnects 5 from rest -> BRIDGE")
    
    print("\nAfter removing bridges, remaining components:")
    print("  Component 1: {0, 1, 2, 3} with edges:")
    print("    (0,1,60), (0,3,10), (0,2,20), (1,2,30), (1,3,80), (2,3,40)")
    
    print("\nMST of component {0,1,2,3}:")
    print("  Edges sorted by weight: (0,3,10), (0,2,20), (1,2,30), (2,3,40), (0,1,60), (1,3,80)")
    print("  MST: (0,3,10) + (0,2,20) + (1,2,30) = 60")

if __name__ == "__main__":
    debug_test1()
    debug_test2()
