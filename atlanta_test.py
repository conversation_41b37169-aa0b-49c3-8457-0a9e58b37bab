from collections import defaultdict

class Graph:
    def __init__(self, vertices):
        self.V = vertices
        self.graph = defaultdict(list)
        self.edges = []
        
    def add_edge(self, u, v, w):
        self.graph[u].append((v, w))
        self.graph[v].append((u, w))
        self.edges.append((u, v, w))
    
    def bridge_util(self, u, visited, parent, low, disc, bridges, time):
        visited[u] = True
        disc[u] = low[u] = time[0]
        time[0] += 1
        
        for v, w in self.graph[u]:
            if not visited[v]:
                parent[v] = u
                self.bridge_util(v, visited, parent, low, disc, bridges, time)
                
                low[u] = min(low[u], low[v])
                
                if low[v] > disc[u]:
                    bridges.add((min(u, v), max(u, v)))
            elif v != parent[u]:
                low[u] = min(low[u], disc[v])
    
    def find_bridges(self):
        visited = [False] * self.V
        disc = [0] * self.V
        low = [0] * self.V
        parent = [-1] * self.V
        bridges = set()
        time = [0]
        
        for i in range(self.V):
            if not visited[i]:
                self.bridge_util(i, visited, parent, low, disc, bridges, time)
        
        return bridges
    
    def dfs(self, v, visited, component, bridge_edges):
        visited[v] = True
        component.append(v)
        
        for neighbor, weight in self.graph[v]:
            edge = (min(v, neighbor), max(v, neighbor))
            if not visited[neighbor] and edge not in bridge_edges:
                self.dfs(neighbor, visited, component, bridge_edges)
    
    def get_components_without_bridges(self):
        bridges = self.find_bridges()
        visited = [False] * self.V
        components = []
        
        for i in range(self.V):
            if not visited[i]:
                component = []
                self.dfs(i, visited, component, bridges)
                if len(component) > 1:  # Only consider components with more than 1 vertex
                    components.append(component)
        
        return components

def find_mst_cost(vertices, edges):
    if len(vertices) <= 1:
        return 0
    
    # Filter edges to only include those between vertices in the component
    vertex_set = set(vertices)
    filtered_edges = []
    
    for u, v, w in edges:
        if u in vertex_set and v in vertex_set:
            filtered_edges.append((w, u, v))
    
    # Sort by weight
    filtered_edges.sort()
    
    # Union-Find
    parent = {v: v for v in vertices}
    
    def find(x):
        if parent[x] != x:
            parent[x] = find(parent[x])
        return parent[x]
    
    mst_cost = 0
    edges_used = 0
    
    for weight, u, v in filtered_edges:
        pu, pv = find(u), find(v)
        if pu != pv:
            parent[pu] = pv
            mst_cost += weight
            edges_used += 1
            if edges_used == len(vertices) - 1:
                break
    
    return mst_cost

def solve(n, k, edges):
    g = Graph(n)
    
    for u, v, w in edges:
        g.add_edge(u, v, w)
    
    components = g.get_components_without_bridges()
    
    if not components:
        return 0
    
    # Find the largest component
    largest_component = max(components, key=len)
    
    return find_mst_cost(largest_component, edges)

# Test cases
def test():
    # Test case 1
    edges1 = [
        (0, 6, 15),
        (0, 1, 10),
        (1, 2, 20),
        (1, 3, 30),
        (2, 3, 40),
        (2, 5, 50),
        (2, 4, 60)
    ]
    result1 = solve(7, 7, edges1)
    print(f"Test 1 result: {result1} (expected: 50)")
    
    # Test case 2
    edges2 = [
        (0, 4, 70),
        (0, 1, 60),
        (0, 3, 10),
        (0, 2, 20),
        (1, 5, 50),
        (1, 2, 30),
        (1, 3, 80),
        (2, 3, 40)
    ]
    result2 = solve(6, 8, edges2)
    print(f"Test 2 result: {result2} (expected: 60)")

if __name__ == "__main__":
    test()
