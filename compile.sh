#!/bin/bash
# Script to compile C++ files with the correct PATH

export PATH=$PATH:/c/ProgramData/mingw64/mingw64/bin

if [ $# -eq 0 ]; then
    echo "Usage: ./compile.sh <filename.cpp> [output_name]"
    echo "Example: ./compile.sh stepwell.cpp stepwell"
    exit 1
fi

input_file=$1
output_name=${2:-${1%.*}}

echo "Compiling $input_file to $output_name..."
g++ -o "$output_name" "$input_file"

if [ $? -eq 0 ]; then
    echo "Compilation successful!"
    echo "Run with: ./$output_name"
else
    echo "Compilation failed!"
fi
